# Sort Helper Utility

A simple and powerful utility for handling multiple column sorting in Prisma queries with automatic field validation.

## Features

- ✅ **Multiple column sorting** - Sort by multiple fields at once
- ✅ **Automatic field validation** - Only allows sorting by specified fields
- ✅ **Relation sorting** - Sort by nested relation fields (e.g., `role.name`)
- ✅ **Type safety** - Works seamlessly with Prisma's orderBy
- ✅ **Simple API** - One function call handles everything
- ✅ **Backward compatible** - Legacy `getOrderBy` function still works

## Quick Start

### Basic Usage

```typescript
import { getModelSort } from "../../utils/sortHelper";

export const viewUsers = async (req, res) => {
  try {
    // Define which fields can be sorted
    const allowedFields = [
      "id",
      "username", 
      "firstName",
      "lastName",
      "email",
      "role.name",        // Relation field
      "created_at"
    ];

    // Get sort configuration from query params
    const orderBy = getModelSort(req.query, allowedFields, "id", "desc");

    const data = await prisma.user.findMany({
      orderBy,
      include: {
        role: true  // Include relations for sorting
      }
    });

    return res.status(200).json(data);
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};
```

### Frontend Usage

```javascript
// Single column sort
GET /api/users?sortBy=username&order=asc

// Multiple column sort
GET /api/users?sortBy=role.name,created_at&order=desc,asc

// Default sorting (if no params provided)
// Will sort by "id" in "desc" order (as specified in getModelSort call)
GET /api/users
```

## API Reference

### `getModelSort(query, allowedFields, defaultField?, defaultOrder?)`

**Parameters:**
- `query` - The request query object (req.query)
- `allowedFields` - Array of field names that can be sorted
- `defaultField` - Default field to sort by (default: "id")
- `defaultOrder` - Default sort order "asc" or "desc" (default: "desc")

**Returns:** Prisma orderBy array

### Field Types

#### Basic Fields
```typescript
const allowedFields = [
  "id",
  "name", 
  "email",
  "created_at"
];
```

#### Relation Fields
```typescript
const allowedFields = [
  "id",
  "role.name",           // User -> Role -> name
  "branch.branch_name",  // User -> Branch -> branch_name
  "client.owner.name"    // Deep relations work too
];
```

## Examples

### With Pagination
```typescript
export const viewTickets = async (req, res) => {
  const { page = 1, pageSize = 10 } = req.query;
  
  const allowedFields = ["id", "title", "priority", "createdAt"];
  const orderBy = getModelSort(req.query, allowedFields, "createdAt", "desc");

  const take = Number(pageSize);
  const skip = (Number(page) - 1) * take;

  const data = await prisma.ticket.findMany({
    orderBy,
    take,
    skip
  });

  return res.status(200).json(data);
};
```

### With Search and Filters
```typescript
export const viewClients = async (req, res) => {
  const { search, country } = req.query;
  
  const allowedFields = ["id", "client_name", "country", "created_at"];
  const orderBy = getModelSort(req.query, allowedFields, "client_name", "asc");

  const whereClause = {
    ...(search && {
      client_name: { contains: search, mode: "insensitive" }
    }),
    ...(country && { country })
  };

  const data = await prisma.client.findMany({
    where: whereClause,
    orderBy
  });

  return res.status(200).json(data);
};
```

## Migration from Old Sort Helper

If you're using the old `getOrderBy` function, you can easily migrate:

```typescript
// Old way
const fieldMapping = {
  username: "username",
  Role: "role.name"
};
const sortByArr = String(sortBy).split(",").map(s => s.trim());
const orderArr = String(order).split(",").map(s => s.trim());
const mappedSortByArr = sortByArr.map(field => fieldMapping[field] || field);
const orderBy = getOrderBy(mappedSortByArr, orderArr, allowedFields);

// New way
const allowedFields = ["username", "role.name"];
const orderBy = getModelSort(req.query, allowedFields, "id", "desc");
```

## Security

- Only fields in `allowedFields` can be sorted
- Invalid fields are automatically filtered out
- SQL injection protection through Prisma
- Type-safe with TypeScript

## Performance Tips

1. **Index your sortable fields** in the database
2. **Limit allowedFields** to only what you need
3. **Use pagination** with sorting for large datasets
4. **Include relations** only when needed for sorting

## Troubleshooting

### Relation sorting not working?
Make sure to include the relation in your Prisma query:

```typescript
const data = await prisma.user.findMany({
  orderBy,
  include: {
    role: true  // Required for role.name sorting
  }
});
```

### Field not sorting?
Check that the field is in your `allowedFields` array and spelled correctly.
