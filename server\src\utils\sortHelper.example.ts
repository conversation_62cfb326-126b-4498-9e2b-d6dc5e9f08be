/**
 * Example usage of the sortHelper utility
 * This file shows how to use the simple sort helper in different controllers
 */

import { getModelSort } from "./sortHelper";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// Example 1: Simple controller with basic sorting
export const viewClients = async (req: any, res: any) => {
  try {
    // Define which fields can be sorted
    const allowedFields = [
      "id",
      "client_name", 
      "owner_name",
      "country",
      "created_at",
      "updated_at"
    ];

    // Get sort configuration from query params
    const orderBy = getModelSort(req.query, allowedFields, "id", "desc");

    const data = await prisma.client.findMany({
      orderBy,
      include: {
        branch: true,
        associate: true
      }
    });

    return res.status(200).json(data);
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

// Example 2: Controller with relation sorting
export const viewUsers = async (req: any, res: any) => {
  try {
    // Include relation fields for sorting
    const allowedFields = [
      "id",
      "username",
      "firstName", 
      "lastName",
      "email",
      "role.name",        // Sort by role name
      "branch.branch_name", // Sort by branch name
      "created_at"
    ];

    const orderBy = getModelSort(req.query, allowedFields, "created_at", "desc");

    const data = await prisma.user.findMany({
      orderBy,
      include: {
        role: true,
        branch: true
      }
    });

    return res.status(200).json(data);
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

// Example 3: Controller with pagination and sorting
export const viewTickets = async (req: any, res: any) => {
  try {
    const { page = 1, pageSize = 10 } = req.query;
    
    const allowedFields = [
      "id",
      "title",
      "priority",
      "owner",
      "pipeline.name",
      "createdAt",
      "updatedAt"
    ];

    const orderBy = getModelSort(req.query, allowedFields, "createdAt", "desc");

    const take = Number(pageSize);
    const skip = (Number(page) - 1) * take;

    const [data, total] = await Promise.all([
      prisma.ticket.findMany({
        orderBy,
        take,
        skip,
        include: {
          pipeline: true
        }
      }),
      prisma.ticket.count()
    ]);

    return res.status(200).json({
      data,
      pagination: {
        page: Number(page),
        pageSize: take,
        total,
        totalPages: Math.ceil(total / take)
      }
    });
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

// Example 4: Multiple sorting with search
export const viewBranches = async (req: any, res: any) => {
  try {
    const { search } = req.query;
    
    const allowedFields = [
      "id",
      "branch_name",
      "corporation.username",
      "created_at"
    ];

    const orderBy = getModelSort(req.query, allowedFields, "branch_name", "asc");

    const whereClause = search ? {
      branch_name: {
        contains: search,
        mode: "insensitive" as const
      }
    } : {};

    const data = await prisma.branch.findMany({
      where: whereClause,
      orderBy,
      include: {
        corporation: true
      }
    });

    return res.status(200).json(data);
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Usage in frontend:
 * 
 * // Single column sort
 * GET /api/clients?sortBy=client_name&order=asc
 * 
 * // Multiple column sort
 * GET /api/users?sortBy=role.name,created_at&order=desc,asc
 * 
 * // With pagination
 * GET /api/tickets?page=1&pageSize=20&sortBy=priority&order=desc
 * 
 * // With search and sort
 * GET /api/branches?search=main&sortBy=branch_name&order=asc
 */
