/**
 * Simple sort helper for Prisma queries with multiple column sorting
 * Automatically handles field validation and relation sorting
 */

interface SortConfig {
  sortBy?: string | string[];
  order?: string | string[];
  allowedFields?: string[];
  defaultSort?: { field: string; order: 'asc' | 'desc' };
}

export function createSortHelper(config: SortConfig = {}) {
  const {
    sortBy,
    order,
    allowedFields = [],
    defaultSort = { field: 'id', order: 'desc' }
  } = config;

  // Convert to arrays
  const sortByArr = Array.isArray(sortBy)
    ? sortBy
    : sortBy ? String(sortBy).split(',').map(s => s.trim()) : [];

  const orderArr = Array.isArray(order)
    ? order
    : order ? String(order).split(',').map(s => s.trim()) : [];

  // Filter valid fields
  const validSortFields = sortByArr.filter(field =>
    field && String(field).trim() &&
    (allowedFields.length === 0 || allowedFields.some(af =>
      af.toLowerCase() === String(field).trim().toLowerCase()
    ))
  );

  // If no valid sort fields, use default
  if (validSortFields.length === 0) {
    return [{ [defaultSort.field]: defaultSort.order }];
  }

  // Build orderBy array for Prisma
  return validSortFields.map((field, idx) => {
    const fieldName = String(field).trim();
    const sortOrder = orderArr[idx] && String(orderArr[idx]).trim().toLowerCase() === 'desc'
      ? 'desc'
      : 'asc';

    // Handle relation fields (e.g., "role.name")
    if (fieldName.includes('.')) {
      const [relation, nestedField] = fieldName.split('.');
      return {
        [relation]: {
          [nestedField]: sortOrder
        }
      };
    }

    return { [fieldName]: sortOrder };
  });
}

/**
 * Legacy function for backward compatibility
 */
export function getOrderBy(
  sortByArr: string[],
  orderArr: string[],
  allowedFields: string[]
) {
  return createSortHelper({
    sortBy: sortByArr,
    order: orderArr,
    allowedFields,
    defaultSort: { field: allowedFields[0] || 'id', order: 'asc' }
  });
}

/**
 * Quick helper for common model sorting patterns
 * Usage: const orderBy = getModelSort(req.query, ['id', 'name', 'created_at']);
 */
export function getModelSort(
  query: any,
  allowedFields: string[],
  defaultField: string = 'id',
  defaultOrder: 'asc' | 'desc' = 'desc'
) {
  return createSortHelper({
    sortBy: query.sortBy,
    order: query.order,
    allowedFields,
    defaultSort: { field: defaultField, order: defaultOrder }
  });
}
